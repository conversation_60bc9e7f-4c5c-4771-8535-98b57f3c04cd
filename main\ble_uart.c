/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "ble_uart.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include <stdio.h>
#include <string.h>

/* UART 配置参数 */
#define UART_PORT_NUM UART_NUM_1
#define UART_BAUD_RATE 115200
#define UART_DATA_BITS UART_DATA_8_BITS
#define UART_PARITY UART_PARITY_DISABLE
#define UART_STOP_BITS UART_STOP_BITS_1
#define UART_FLOW_CTRL UART_HW_FLOWCTRL_DISABLE
#define UART_SOURCE_CLK UART_SCLK_DEFAULT
//
#define UART_TX_PIN GPIO_NUM_15
#define UART_RX_PIN GPIO_NUM_23
#define UART_RTS_PIN UART_PIN_NO_CHANGE
#define UART_CTS_PIN UART_PIN_NO_CHANGE
//
#define UART_BUF_SIZE 1024
#define UART_RX_BUF_SIZE 2048
#define UART_QUEUE_SIZE 20
//
static const char *TAG = "BLE_UART";
static bool uart_initialized = false;

/* 接收相关变量 */
static QueueHandle_t uart_queue = NULL;
static TaskHandle_t uart_recv_task_handle = NULL;
static ble_uart_recv_callback_t recv_callback = NULL;
static uint32_t recv_timeout_ms = 100; // 默认100ms超时
static esp_timer_handle_t timeout_timer = NULL;
static uint8_t *recv_buffer = NULL;
static size_t recv_buffer_pos = 0;
static bool recv_active = false;

// 初始化
esp_err_t ble_uart_init(void)
{
    esp_err_t ret;
    //
    if (uart_initialized)
    {
        ESP_LOGW(TAG, "UART已初始化");
        return ESP_OK;
    }
    // 安装UART驱动
    ret = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return ret;
    }
    // 配置UART参数
    const uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_BITS,
        .parity = UART_PARITY,
        .stop_bits = UART_STOP_BITS,
        .flow_ctrl = UART_FLOW_CTRL,
        .source_clk = UART_SOURCE_CLK,
    };
    //
    ret = uart_param_config(UART_PORT_NUM, &uart_config);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure UART parameters: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    // 设置UART引脚
    ret = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_RTS_PIN, UART_CTS_PIN);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    uart_initialized = true;
    ESP_LOGI(TAG, "UART 初始货品成功 (TX: GPIO%d, RX: GPIO%d, Baud: %d)",
             UART_TX_PIN, UART_RX_PIN, UART_BAUD_RATE);

    return ESP_OK;
}

// 释放
esp_err_t ble_uart_deinit(void)
{
    if (!uart_initialized)
    {
        ESP_LOGW(TAG, "UART not initialized");
        return ESP_OK;
    }

    // 先停止接收功能
    if (recv_active)
    {
        ble_uart_stop_recv();
    }

    esp_err_t ret = uart_driver_delete(UART_PORT_NUM);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to delete UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    // 清理所有接收相关资源
    recv_callback = NULL;
    uart_queue = NULL;

    uart_initialized = false;
    ESP_LOGI(TAG, "UART deinitialized successfully");

    return ESP_OK;
}

//
esp_err_t ble_uart_send(const uint8_t *data, size_t length)
{
    if (!uart_initialized)
    {
        ESP_LOGE(TAG, "UART 未初始化");
        return ESP_FAIL;
    }

    if (data == NULL)
    {
        ESP_LOGE(TAG, "Invalid data pointer");
        return ESP_ERR_INVALID_ARG;
    }

    if (length == 0)
    {
        ESP_LOGW(TAG, "Data length is zero");
        return ESP_OK;
    }

    // 通过UART发送原始二进制数据
    int bytes_written = uart_write_bytes(UART_PORT_NUM, data, length);
    if (bytes_written != length)
    {
        ESP_LOGW(TAG, "UART 发送中: written %d of %zu bytes", bytes_written, length);
        return ESP_FAIL;
    }

    ESP_LOGD(TAG, "UART 已发送: %zu bytes", length);
    // 以HEX形式打印数据缓冲区内容
    ESP_LOG_BUFFER_HEXDUMP(TAG, data, length, ESP_LOG_DEBUG);

    return ESP_OK;
}

/**
 * @brief 超时定时器回调函数
 *
 * 当接收超时时调用，处理当前接收缓冲区中的数据
 */
static void uart_timeout_callback(void *arg)
{
    if (recv_buffer_pos > 0 && recv_callback != NULL)
    {
        ESP_LOGD(TAG, "UART receive timeout, processing %zu bytes", recv_buffer_pos);
        recv_callback(recv_buffer, recv_buffer_pos);
        recv_buffer_pos = 0;
    }
}

/**
 * @brief UART接收任务
 *
 * 处理UART事件队列，接收数据并管理超时
 */
static void uart_recv_task(void *pvParameters)
{
    uart_event_t event;
    uint8_t *temp_buffer = malloc(UART_BUF_SIZE);
    if (temp_buffer == NULL)
    {
        ESP_LOGE(TAG, "Failed to allocate temporary buffer for UART receive task");
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "UART receive task started");

    while (recv_active)
    {
        if (xQueueReceive(uart_queue, (void *)&event, pdMS_TO_TICKS(100)))
        {
            switch (event.type)
            {
            case UART_DATA:
                if (event.size > 0)
                {
                    // 读取UART数据
                    int len = uart_read_bytes(UART_PORT_NUM, temp_buffer, event.size, pdMS_TO_TICKS(100));
                    if (len > 0)
                    {
                        // 检查接收缓冲区是否有足够空间
                        if (recv_buffer_pos + len <= UART_RX_BUF_SIZE)
                        {
                            memcpy(recv_buffer + recv_buffer_pos, temp_buffer, len);
                            recv_buffer_pos += len;

                            ESP_LOGD(TAG, "UART received %d bytes, total: %zu", len, recv_buffer_pos);

                            // 重启超时定时器
                            if (timeout_timer != NULL)
                            {
                                esp_timer_stop(timeout_timer);
                                esp_timer_start_once(timeout_timer, recv_timeout_ms * 1000);
                            }
                        }
                        else
                        {
                            ESP_LOGW(TAG, "UART receive buffer overflow, discarding data");
                            recv_buffer_pos = 0;
                        }
                    }
                }
                break;

            case UART_FIFO_OVF:
                ESP_LOGW(TAG, "UART FIFO overflow");
                uart_flush_input(UART_PORT_NUM);
                break;

            case UART_BUFFER_FULL:
                ESP_LOGW(TAG, "UART ring buffer full");
                uart_flush_input(UART_PORT_NUM);
                break;

            case UART_BREAK:
                ESP_LOGD(TAG, "UART break detected");
                break;

            case UART_PARITY_ERR:
                ESP_LOGW(TAG, "UART parity error");
                break;

            case UART_FRAME_ERR:
                ESP_LOGW(TAG, "UART frame error");
                break;

            default:
                ESP_LOGD(TAG, "UART event type: %d", event.type);
                break;
            }
        }
    }

    free(temp_buffer);
    ESP_LOGI(TAG, "UART receive task ended");
    vTaskDelete(NULL);
}

esp_err_t ble_uart_set_recv_callback(ble_uart_recv_callback_t callback, uint32_t timeout_ms)
{
    if (!uart_initialized)
    {
        return ESP_FAIL;
    }
    if (timeout_ms == 0)
    {
        return ESP_ERR_INVALID_ARG;
    }

    recv_callback = callback;
    recv_timeout_ms = timeout_ms;

    ESP_LOGI(TAG, "UART receive callback set, timeout: %lu ms", recv_timeout_ms);
    return ESP_OK;
}

esp_err_t ble_uart_start_recv(void)
{
    if (!uart_initialized)
    {
        ESP_LOGE(TAG, "UART not initialized");
        return ESP_FAIL;
    }

    if (recv_active)
    {
        ESP_LOGW(TAG, "UART receive already active");
        return ESP_OK;
    }

    if (recv_callback == NULL)
    {
        ESP_LOGE(TAG, "No receive callback set");
        return ESP_FAIL;
    }

    // 分配接收缓冲区
    if (recv_buffer == NULL)
    {
        recv_buffer = malloc(UART_RX_BUF_SIZE);
        if (recv_buffer == NULL)
        {
            ESP_LOGE(TAG, "Failed to allocate receive buffer");
            return ESP_FAIL;
        }
    }
    recv_buffer_pos = 0;

    // 创建超时定时器
    if (timeout_timer == NULL)
    {
        esp_timer_create_args_t timer_args = {
            .callback = uart_timeout_callback,
            .arg = NULL,
            .name = "uart_timeout"};

        esp_err_t ret = esp_timer_create(&timer_args, &timeout_timer);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create timeout timer: %s", esp_err_to_name(ret));
            free(recv_buffer);
            recv_buffer = NULL;
            return ret;
        }
    }

    // 重新安装UART驱动以启用事件队列
    uart_driver_delete(UART_PORT_NUM);

    esp_err_t ret = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE * 2, UART_BUF_SIZE * 2,
                                        UART_QUEUE_SIZE, &uart_queue, 0);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to reinstall UART driver with queue: %s", esp_err_to_name(ret));
        esp_timer_delete(timeout_timer);
        timeout_timer = NULL;
        free(recv_buffer);
        recv_buffer = NULL;
        return ret;
    }

    // 启动接收任务
    recv_active = true;
    BaseType_t task_ret = xTaskCreate(uart_recv_task, "uart_recv_task", 4096, NULL, 5, &uart_recv_task_handle);
    if (task_ret != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create UART receive task");
        recv_active = false;
        uart_driver_delete(UART_PORT_NUM);
        esp_timer_delete(timeout_timer);
        timeout_timer = NULL;
        free(recv_buffer);
        recv_buffer = NULL;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "UART receive started successfully");
    return ESP_OK;
}

esp_err_t ble_uart_stop_recv(void)
{
    if (!uart_initialized)
    {
        ESP_LOGE(TAG, "UART not initialized");
        return ESP_FAIL;
    }

    if (!recv_active)
    {
        ESP_LOGW(TAG, "UART receive not active");
        return ESP_OK;
    }

    // 停止接收任务
    recv_active = false;

    // 等待任务结束
    if (uart_recv_task_handle != NULL)
    {
        // 给任务一些时间自然结束
        vTaskDelay(pdMS_TO_TICKS(200));

        // 如果任务还在运行，强制删除
        if (eTaskGetState(uart_recv_task_handle) != eDeleted)
        {
            vTaskDelete(uart_recv_task_handle);
        }
        uart_recv_task_handle = NULL;
    }

    // 停止并删除超时定时器
    if (timeout_timer != NULL)
    {
        esp_timer_stop(timeout_timer);
        esp_timer_delete(timeout_timer);
        timeout_timer = NULL;
    }

    // 释放接收缓冲区
    if (recv_buffer != NULL)
    {
        free(recv_buffer);
        recv_buffer = NULL;
        recv_buffer_pos = 0;
    }

    // 重新安装UART驱动（不带事件队列）
    uart_driver_delete(UART_PORT_NUM);
    uart_queue = NULL;

    esp_err_t ret = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to reinstall UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "UART receive stopped successfully");
    return ESP_OK;
}
